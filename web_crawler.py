import os
import re
import time
import logging
import requests
import pandas as pd
from urllib.parse import urljoin, urlparse
from pathlib import Path
import defusedxml.ElementTree as ET
from bs4 import BeautifulSoup
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
import mimetypes
from typing import List, Dict, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProductCrawler:
    def __init__(self, sitemap_url: str, output_dir: str = "downloads", rate_limit: float = 1.0):
        self.sitemap_url = sitemap_url
        self.output_dir = Path(output_dir)
        self.rate_limit = rate_limit
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Create output directory
        self.output_dir.mkdir(exist_ok=True)
        self.images_dir = self.output_dir / "images"
        self.images_dir.mkdir(exist_ok=True)
        
        self.products_data = []

    def download_with_retry(self, url: str, max_retries: int = 3) -> Optional[requests.Response]:
        """Download content with retry logic"""
        for attempt in range(max_retries):
            try:
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                return response
            except requests.RequestException as e:
                logger.warning(f"Attempt {attempt + 1} failed for {url}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
        return None

    def parse_sitemap(self) -> Tuple[List[str], List[Dict]]:
        """Parse XML sitemap and extract URLs and image information"""
        logger.info(f"Downloading sitemap: {self.sitemap_url}")
        response = self.download_with_retry(self.sitemap_url)
        if not response:
            raise Exception(f"Failed to download sitemap: {self.sitemap_url}")

        try:
            root = ET.fromstring(response.content)
        except ET.ParseError as e:
            logger.error(f"XML parsing error: {e}")
            raise

        # Extract namespaces
        namespaces = {
            'sitemap': 'http://www.sitemaps.org/schemas/sitemap/0.9',
            'image': 'http://www.google.com/schemas/sitemap-image/1.1'
        }

        product_urls = []
        image_data = []

        for url_element in root.findall('.//sitemap:url', namespaces):
            # Extract product URL
            loc_element = url_element.find('sitemap:loc', namespaces)
            if loc_element is not None:
                product_urls.append(loc_element.text)

            # Extract product name from attributes
            product_name = None
            for attr in url_element.findall('.//Attribute[@name="name"]'):
                product_name = attr.text
                break

            # Extract image URLs
            for image_element in url_element.findall('.//image:image', namespaces):
                image_loc = image_element.find('image:loc', namespaces)
                if image_loc is not None:
                    image_data.append({
                        'url': image_loc.text,
                        'product_name': product_name or 'unknown_product'
                    })

        logger.info(f"Found {len(product_urls)} product URLs and {len(image_data)} images")
        return product_urls, image_data

    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for filesystem compatibility"""
        return re.sub(r'[<>:"/\\|?*]', '_', filename)

    def download_images(self, image_data: List[Dict]) -> Dict[str, List[str]]:
        """Download images and organize by product"""
        product_images = {}
        
        for img_info in image_data:
            product_name = self.sanitize_filename(img_info['product_name'])
            image_url = img_info['url']
            
            # Create product-specific folder
            product_folder = self.images_dir / product_name
            product_folder.mkdir(exist_ok=True)
            
            # Download image
            logger.info(f"Downloading image: {image_url}")
            response = self.download_with_retry(image_url)
            if not response:
                logger.error(f"Failed to download image: {image_url}")
                continue

            # Determine file extension
            content_type = response.headers.get('content-type', '')
            extension = mimetypes.guess_extension(content_type) or '.jpg'
            
            # Generate unique filename
            base_name = urlparse(image_url).path.split('/')[-1]
            if not base_name:
                base_name = "image"
            
            filename = base_name
            if not filename.endswith(extension):
                filename += extension
            
            file_path = product_folder / filename
            counter = 1
            while file_path.exists():
                name, ext = os.path.splitext(filename)
                file_path = product_folder / f"{name}_{counter}{ext}"
                counter += 1

            # Save image
            try:
                with open(file_path, 'wb') as f:
                    f.write(response.content)
                
                # Track image for product
                if product_name not in product_images:
                    product_images[product_name] = []
                product_images[product_name].append(str(file_path))
                
                logger.info(f"Saved image: {file_path}")
            except Exception as e:
                logger.error(f"Error saving image {file_path}: {e}")

            time.sleep(self.rate_limit)

        return product_images

    def extract_product_data(self, url: str) -> Dict:
        """Extract product information from webpage"""
        logger.info(f"Scraping product page: {url}")
        response = self.download_with_retry(url)
        if not response:
            logger.error(f"Failed to download page: {url}")
            return {}

        soup = BeautifulSoup(response.content, 'html.parser')
        product_data = {'url': url}

        # Extract product name
        title_element = soup.select_one('h1.page-title span.base[data-ui-id="page-title-wrapper"]')
        product_data['NOME'] = title_element.get_text(strip=True) if title_element else 'N/A'

        # Extract price
        price_element = soup.select_one('span.price')
        product_data['PREZZO'] = price_element.get_text(strip=True) if price_element else 'N/A'

        # Extract characteristics and EAN
        characteristics = []
        ean_code = 'N/A'
        
        attributes_container = soup.select_one('div.attribute-container div.attributes-block div.additional-attributes')
        if attributes_container:
            for attribute in attributes_container.select('div.attribute-single'):
                label_elem = attribute.select_one('div.col.label')
                data_elem = attribute.select_one('div.col.data.feature')
                
                if label_elem and data_elem:
                    label = label_elem.get_text(strip=True)
                    value = data_elem.get_text(strip=True)
                    
                    if 'EAN' in label.upper():
                        ean_code = value
                    else:
                        characteristics.append(f"{label}: {value}")

        product_data['CARATTERISTICHE'] = '; '.join(characteristics) if characteristics else 'N/A'
        product_data['EAN_CODE'] = ean_code

        return product_data

    def crawl(self):
        """Main crawling method"""
        try:
            # Parse sitemap
            product_urls, image_data = self.parse_sitemap()
            
            # Download images
            logger.info("Starting image downloads...")
            product_images = self.download_images(image_data)
            
            # Scrape product pages
            logger.info("Starting product page scraping...")
            for url in product_urls:
                product_data = self.extract_product_data(url)
                if product_data:
                    # Add image paths
                    product_name = self.sanitize_filename(product_data.get('NOME', 'unknown'))
                    product_data['IMMAGINI'] = '; '.join(product_images.get(product_name, []))
                    
                    self.products_data.append(product_data)
                
                time.sleep(self.rate_limit)
            
            # Save to Excel
            self.save_to_excel()
            
        except Exception as e:
            logger.error(f"Crawling failed: {e}")
            raise

    def save_to_excel(self):
        """Save extracted data to Excel file"""
        if not self.products_data:
            logger.warning("No product data to save")
            return

        df = pd.DataFrame(self.products_data)
        
        # Reorder columns
        columns_order = ['NOME', 'PREZZO', 'CARATTERISTICHE', 'EAN_CODE', 'IMMAGINI']
        for col in columns_order:
            if col not in df.columns:
                df[col] = 'N/A'
        
        df = df[columns_order + [col for col in df.columns if col not in columns_order]]
        
        output_file = self.output_dir / "products_data.xlsx"
        df.to_excel(output_file, index=False, engine='openpyxl')
        
        logger.info(f"Data saved to {output_file}")
        logger.info(f"Total products processed: {len(self.products_data)}")

def main():
    # Configuration
    sitemap_url = "https://example.com/sitemap.xml"  # Replace with actual sitemap URL
    output_directory = "crawler_output"
    rate_limit_seconds = 1.0  # Delay between requests
    
    # Create and run crawler
    crawler = ProductCrawler(
        sitemap_url=sitemap_url,
        output_dir=output_directory,
        rate_limit=rate_limit_seconds
    )
    
    try:
        crawler.crawl()
        print("Crawling completed successfully!")
    except Exception as e:
        print(f"Crawling failed: {e}")
        logger.error(f"Crawling failed: {e}")

if __name__ == "__main__":
    main()